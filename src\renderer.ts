import './index.css';

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  console.log('Application initialized');

  // Add window resize handler for responsive behavior
  window.addEventListener('resize', () => {
    // Trigger any responsive adjustments if needed
    const app = document.getElementById('app');
    if (app) {
      app.style.height = `${window.innerHeight}px`;
    }
  });

  // Prevent default drag and drop behavior
  document.addEventListener('dragover', (e) => {
    e.preventDefault();
  });

  document.addEventListener('drop', (e) => {
    e.preventDefault();
  });
});
