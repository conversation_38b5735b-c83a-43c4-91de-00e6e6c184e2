/* CSS Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  overflow: hidden;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  background-color: #1a1a1a;
  color: #e0e0e0;
  line-height: 1.6;
  font-size: 14px;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* App Container */
#app {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100vw;
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
}

/* Header Styles */
.app-header {
  background-color: #2d2d2d;
  border-bottom: 1px solid #404040;
  padding: 1rem 2rem;
  flex-shrink: 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.app-header h1 {
  color: #ffffff;
  font-size: 1.5rem;
  font-weight: 600;
  letter-spacing: -0.025em;
}

/* Main Content Area */
.app-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding: 2rem;
}

.content-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
  width: 100%;
}

.content-container p {
  font-size: 1.1rem;
  color: #b0b0b0;
  margin-bottom: 2rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .app-header {
    padding: 0.75rem 1rem;
  }

  .app-header h1 {
    font-size: 1.25rem;
  }

  .app-main {
    padding: 1rem;
  }

  .content-container p {
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .app-header {
    padding: 0.5rem 0.75rem;
  }

  .app-header h1 {
    font-size: 1.1rem;
  }

  .app-main {
    padding: 0.75rem;
  }

  .content-container p {
    font-size: 0.9rem;
  }
}

/* Scrollbar Styling for Dark Theme */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #2d2d2d;
}

::-webkit-scrollbar-thumb {
  background: #555555;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #666666;
}

/* Selection Styling */
::selection {
  background-color: #4a9eff;
  color: #ffffff;
}

::-moz-selection {
  background-color: #4a9eff;
  color: #ffffff;
}
